#!/usr/bin/env python3
"""
Simple test to check if quotation templates can be retrieved from Firebase Storage.
"""

import requests
import json
import os

# Test configuration
BASE_URL = "http://localhost:5000/api"  # Adjust if your backend runs on a different port
TEST_EMAIL = "<EMAIL>"  # Adjust to your admin email
TEST_PASSWORD = "admin123"  # Adjust to your admin password

def test_quotation_templates():
    """Test quotation template functionality."""
    print("Testing quotation template Firebase Storage integration...")
    
    # Step 1: Login to get authentication token
    print("1. Logging in...")
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code != 200:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
        
        token = response.json().get("access_token")
        if not token:
            print("❌ No access token received")
            return False
        
        print("✅ Login successful")
        
        # Set up headers for authenticated requests
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return False
    
    # Step 2: Get all quotation templates
    print("2. Fetching quotation templates...")
    try:
        response = requests.get(f"{BASE_URL}/quotation-templates", headers=headers)
        if response.status_code != 200:
            print(f"❌ Failed to fetch templates: {response.status_code} - {response.text}")
            return False
        
        templates_data = response.json()
        templates = templates_data.get("templates", [])
        print(f"✅ Found {len(templates)} quotation templates")
        
        if len(templates) == 0:
            print("ℹ️  No templates found. This is normal if none have been uploaded yet.")
            return True
        
        # Display template info
        for template in templates:
            print(f"   - {template['name']} (ID: {template['id']}) - Path: {template['file_path']}")
        
    except Exception as e:
        print(f"❌ Error fetching templates: {str(e)}")
        return False
    
    # Step 3: Test downloading a template if any exist
    if templates:
        print("3. Testing template download...")
        template_id = templates[0]['id']
        template_name = templates[0]['name']
        
        try:
            # Update headers for file download
            download_headers = {
                "Authorization": f"Bearer {token}"
            }
            
            response = requests.get(
                f"{BASE_URL}/quotation-templates/{template_id}/download", 
                headers=download_headers
            )
            
            if response.status_code == 200:
                print(f"✅ Successfully downloaded template '{template_name}' ({len(response.content)} bytes)")
                
                # Check if it's a valid file
                if len(response.content) > 0:
                    print("✅ Template file has content")
                else:
                    print("❌ Template file is empty")
                    return False
                    
            else:
                print(f"❌ Failed to download template: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error downloading template: {str(e)}")
            return False
    
    # Step 4: Test getting default template
    print("4. Testing default template retrieval...")
    try:
        response = requests.get(f"{BASE_URL}/quotation-templates/default", headers=headers)
        if response.status_code == 200:
            default_template = response.json()
            print(f"✅ Default template found: {default_template['name']}")
        elif response.status_code == 404:
            print("ℹ️  No default template set (this is normal)")
        else:
            print(f"❌ Error getting default template: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error getting default template: {str(e)}")
        return False
    
    print("\n🎉 All quotation template tests passed!")
    print("✅ Quotation templates are working correctly with Firebase Storage")
    return True

def main():
    """Run the test."""
    print("=" * 60)
    print("QUOTATION TEMPLATE FIREBASE STORAGE TEST")
    print("=" * 60)
    
    success = test_quotation_templates()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ TEST RESULT: SUCCESS")
        print("Quotation templates are properly integrated with Firebase Storage!")
    else:
        print("❌ TEST RESULT: FAILED")
        print("There are issues with the quotation template Firebase Storage integration.")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
