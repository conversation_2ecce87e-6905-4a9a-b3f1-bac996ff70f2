import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Quotation, QuotationItem } from '../../types/quotation';
import { Product } from '../../types/product';
import { createQuotation, getQuotationById, updateQuotation, addItemToQuotation, updateQuotationItem, deleteQuotationItem } from '../../services/quotationService';
import { getAllCustomersNoPage, searchCustomers } from '../../services/customerService';
import { getAllQuotationTemplates, getDefaultQuotationTemplate, generateQuotationDocument } from '../../services/quotationTemplateService';
import { QuotationTemplate } from '../../types/quotation_template';
import { Customer } from '../../types/customer';
import { useAuth } from '../../context/AuthContext';
import { useConfirmation } from '../../context/ConfirmationContext';
import { FaSave, FaTimes, FaFileAlt, FaArrowLeft, FaUserPlus } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';
import ProductSelector from './ProductSelector';
import QuotationItemList from './QuotationItemList';
import NewCustomerModal from '../customers/NewCustomerModal';
import { LABOR_HOUR_RATE } from '../../constants/pricing';

import * as Yup from 'yup';

// Validation schema
const quotationSchema = Yup.object().shape({
  customer_id: Yup.number().required('Klant is verplicht'),
  title: Yup.string().required('Titel is verplicht'),
  valid_until: Yup.date().nullable().typeError('Ongeldige datum'),
  discount_percentage: Yup.number().min(0, 'Korting kan niet negatief zijn').max(100, 'Korting kan niet meer dan 100% zijn').nullable(),
  vat_percentage: Yup.number().min(0, 'BTW kan niet negatief zijn').nullable(),
});

const QuotationForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();

  const [formData, setFormData] = useState<Partial<Quotation>>({
    customer_id: undefined,
    title: '',
    introduction: '',
    conclusion: '',
    discount_percentage: 0,
    vat_percentage: 21,
    status: 'concept',
    valid_until: (() => {
      // Create a date 30 days from now
      const date = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
      // Format as YYYY-MM-DDThh:mm for the datetime-local input
      return date.toISOString().slice(0, 16);
    })()
  });

  const [customers, setCustomers] = useState<Customer[]>([]);
  const [items, setItems] = useState<QuotationItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isEditing, setIsEditing] = useState(false);

  // Customer search state
  const [customerSearch, setCustomerSearch] = useState('');
  const [searchResults, setSearchResults] = useState<Customer[]>([]);
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // New customer modal state
  const [showNewCustomerModal, setShowNewCustomerModal] = useState(false);

  // Template state
  const [templates, setTemplates] = useState<QuotationTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<QuotationTemplate | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch customers
        const customersResponse = await getAllCustomersNoPage();
        setCustomers(customersResponse.customers);

        // Fetch quotation templates
        try {
          const templatesResponse = await getAllQuotationTemplates(true);
          setTemplates(templatesResponse.templates);
          console.log('Loaded templates:', templatesResponse.templates);

          // Set default template
          try {
            const defaultTemplate = await getDefaultQuotationTemplate();
            setSelectedTemplate(defaultTemplate);
            console.log('Set default template:', defaultTemplate);
          } catch (err) {
            // No default template found, that's okay
            console.log('No default template found');
            // If no default, select first available template
            if (templatesResponse.templates.length > 0) {
              setSelectedTemplate(templatesResponse.templates[0]);
            }
          }
        } catch (err) {
          console.error('Failed to load templates:', err);
          // Continue without templates
        }

        // If editing, fetch quotation
        if (id) {
          setIsEditing(true);
          const quotation = await getQuotationById(parseInt(id));
          setFormData({
            ...quotation,
            valid_until: quotation.valid_until ? (() => {
              // Parse the date string from the API
              const date = new Date(quotation.valid_until);
              // Format as YYYY-MM-DDThh:mm for the datetime-local input
              return date.toISOString().slice(0, 16);
            })() : null
          });
          setItems(quotation.items);

          // Set customer search value if editing a quotation with a customer
          if (quotation.customer_id) {
            const selectedCustomer = customersResponse.customers.find(c => c.id === quotation.customer_id);
            if (selectedCustomer) {
              setCustomerSearch(selectedCustomer.name);
            }
          }
        }
      } catch (err: any) {
        console.error('Failed to fetch data:', err);
        setErrors({
          form: err.response?.data?.error || 'Fout bij ophalen van gegevens'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.customer-search-container') && !target.closest('.browse-customers-btn')) {
        setShowCustomerDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Load search results when the search term changes
  useEffect(() => {
    // Don't search if the term is too short
    if (customerSearch.trim().length < 2) {
      // Keep existing results if dropdown is showing
      if (!showCustomerDropdown) {
        setSearchResults([]);
      }
      return;
    }

    const fetchSearchResults = async () => {
      setIsSearching(true);
      try {
        const response = await searchCustomers(customerSearch);
        setSearchResults(response.customers);
      } catch (error) {
        console.error('Error searching customers:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    // Debounce the search to avoid too many API calls
    const timeoutId = setTimeout(() => {
      fetchSearchResults();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [customerSearch, showCustomerDropdown]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Convert to number for number inputs
    let parsedValue: string | number | null = value;
    if (type === 'number') {
      parsedValue = value ? parseFloat(value) : null;
    } else if (type === 'date' || type === 'datetime-local') {
      // For date inputs, ensure the value is a valid date string or null
      parsedValue = value || null;
    }

    setFormData({
      ...formData,
      [name]: parsedValue
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };



  const validateForm = async (): Promise<boolean> => {
    try {
      await quotationSchema.validate(formData, { abortEarly: false });
      return true;
    } catch (err) {
      if (err instanceof Yup.ValidationError) {
        const validationErrors: Record<string, string> = {};
        err.inner.forEach((error) => {
          if (error.path) {
            validationErrors[error.path] = error.message;
          }
        });
        setErrors(validationErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isValid = await validateForm();
    if (!isValid) return;

    // Check if there are items
    if (items.length === 0) {
      setErrors({
        form: 'Voeg minimaal één product toe aan de offerte'
      });
      return;
    }

    try {
      setSubmitting(true);

      let quotationId: number;

      if (isEditing && id) {
        // Update existing quotation
        console.log('Updating quotation with data:', formData);

        // Only send the fields we want to update
        const dataToUpdate = {
          title: formData.title,
          customer_id: formData.customer_id,
          status: formData.status,
          introduction: formData.introduction,
          conclusion: formData.conclusion,
          discount_percentage: formData.discount_percentage,
          vat_percentage: formData.vat_percentage,
          valid_until: formData.valid_until
        };

        const updatedQuotation = await updateQuotation(parseInt(id), dataToUpdate);
        quotationId = updatedQuotation.id;

        // Get existing items from the server
        const existingQuotation = await getQuotationById(quotationId);
        const existingItemIds = existingQuotation.items.map(item => item.id);

        // Find items that need to be deleted (exist on server but not in local state)
        const currentItemIds = items.filter(item => item.id > 0).map(item => item.id);
        const itemsToDelete = existingItemIds.filter(id => !currentItemIds.includes(id));

        // Delete items that were removed
        for (const itemId of itemsToDelete) {
          try {
            await deleteQuotationItem(itemId);
          } catch (err) {
            console.error(`Failed to delete item ${itemId}: ${err}`);
          }
        }

        // Update all items
        for (const item of items) {
          try {
            if (item.id > 0) {
              // Update existing item
              await updateQuotationItem(item.id, {
                description: item.description,
                quantity: item.quantity,
                unit_price: item.unit_price,
                discount_percentage: item.discount_percentage,
                sort_order: item.sort_order
              });
            } else {
              // Add new item
              await addItemToQuotation(quotationId, {
                product_id: item.product_id,
                description: item.description || (item.product_name || ''),
                quantity: item.quantity,
                unit_price: item.unit_price,
                discount_percentage: item.discount_percentage,
                sort_order: item.sort_order
              });
            }
          } catch (err) {
            console.error(`Failed to update/add item to quotation: ${err}`);
            // Continue with other items even if one fails
          }
        }
      } else {
        // Create new quotation
        const newQuotation = await createQuotation({
          ...formData,
          created_by: user?.id
        });
        quotationId = newQuotation.id;

        // Add items to the new quotation
        for (const item of items) {
          try {
            await addItemToQuotation(quotationId, {
              product_id: item.product_id,
              description: item.description || (item.product_name || ''), // Ensure description is not empty
              quantity: item.quantity,
              unit_price: item.unit_price,
              discount_percentage: item.discount_percentage,
              sort_order: item.sort_order
            });
          } catch (err) {
            console.error(`Failed to add item to quotation: ${err}`);
            // Continue with other items even if one fails
          }
        }
      }

      navigate(`/quotations/${quotationId}`);
    } catch (err: any) {
      console.error('Failed to save quotation:', err);

      // Handle validation errors from the server
      if (err.details) {
        console.log('Validation errors:', err.details);
        setErrors(err.details);
      } else {
        setErrors({
          form: err.message || 'Fout bij opslaan van offerte'
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleProductSelected = (product: Product, laborHours?: number) => {
    // Gebruik de selling_price die al berekend is in het backend model
    const customerPrice = product.selling_price || 0;

    // Create a new item for the product
    const newItem: Partial<QuotationItem> = {
      id: -Date.now(), // Temporary negative ID for new items
      quotation_id: formData.id || 0,
      product_id: product.id,
      product_name: product.name,
      product_code: product.product_code,
      // Always set description to product name to ensure it's not empty
      description: product.name || `Product ${product.id}`,
      quantity: 1,
      unit_price: customerPrice, // This is the selling price for the customer
      discount_percentage: 0,
      sort_order: items.length,
      total_price: customerPrice // Initially the same as unit_price * quantity
    };

    // Create a new array with the new items
    const newItems = [...items, newItem as QuotationItem];

    // If labor hours are provided, add a labor item
    if (laborHours && laborHours > 0) {
      const laborCost = laborHours * LABOR_HOUR_RATE;

      const laborItem: Partial<QuotationItem> = {
        id: -(Date.now() + 1), // Another temporary negative ID
        quotation_id: formData.id || 0,
        product_id: null, // Labor items don't have a product_id
        product_name: null,
        product_code: null,
        description: `Arbeid installatie ${product.name}`,
        quantity: laborHours,
        unit_price: LABOR_HOUR_RATE,
        discount_percentage: 0,
        sort_order: items.length + 1,
        total_price: laborCost
      };

      newItems.push(laborItem as QuotationItem);
    }

    console.log('Adding new items to quotation:', newItems);
    setItems(newItems);
  };

  const handleDeleteItem = (itemId: number) => {
    // Find the item being deleted
    const itemToDelete = items.find(item => item.id === itemId);
    if (!itemToDelete) return;

    // Controleer of dit een arbeidsitem is - zo ja, dan mag het niet direct verwijderd worden
    const isLaborItem = !itemToDelete.product_id && itemToDelete.description?.toLowerCase().includes('arbeid');
    if (isLaborItem) {
      // Arbeidsitems kunnen niet direct worden verwijderd
      return;
    }

    // Check if this is a product item with a related labor item
    const isProductItem = itemToDelete.product_id !== null;

    // Find any labor items that might be related to this product
    const relatedLaborItem = isProductItem ? items.find(item =>
      item.product_id === null &&
      item.description === `Arbeid installatie ${itemToDelete.product_name}`
    ) : null;

    // Just remove from local state, we'll handle server updates when the form is submitted
    if (relatedLaborItem) {
      // Remove both the item and its related labor item
      setItems(items.filter(item => item.id !== itemId && item.id !== relatedLaborItem.id));
    } else {
      // Just remove the item
      setItems(items.filter(item => item.id !== itemId));
    }
  };

  const handleUpdateItem = (itemId: number, data: Partial<QuotationItem>) => {
    // Find the item being updated
    const itemToUpdate = items.find(item => item.id === itemId);
    if (!itemToUpdate) return;

    // Check if this is a product item with a related labor item
    const isProductItem = itemToUpdate.product_id !== null;
    const hasQuantityChange = data.quantity !== undefined && data.quantity !== itemToUpdate.quantity;

    // Find any labor items that might be related to this product
    const relatedLaborItem = isProductItem ? items.find(item =>
      item.product_id === null &&
      item.description === `Arbeid installatie ${itemToUpdate.product_name}`
    ) : null;

    // Update the items
    const updatedItems = items.map(item => {
      // Update the current item
      if (item.id === itemId) {
        const updatedItem = { ...item, ...data };

        // Recalculate total price and round to 2 decimal places
        updatedItem.total_price = Math.round((updatedItem.quantity * updatedItem.unit_price) * 100) / 100;
        if (updatedItem.discount_percentage) {
          updatedItem.total_price = Math.round((updatedItem.total_price * (1 - (updatedItem.discount_percentage / 100))) * 100) / 100;
        }

        return updatedItem;
      }

      // Als dit een gerelateerd arbeidsitem is en de hoeveelheid van het product is gewijzigd
      if (relatedLaborItem && item.id === relatedLaborItem.id && hasQuantityChange) {
        // Update het arbeidsitem met de nieuwe hoeveelheid
        const updatedLaborItem = {
          ...item,
          quantity: data.quantity || 1 // Standaard 1 als undefined
        };

        // Herbereken de totaalprijs en rond af op 2 decimalen
        updatedLaborItem.total_price = Math.round((updatedLaborItem.quantity * updatedLaborItem.unit_price) * 100) / 100;

        return updatedLaborItem;
      }

      return item;
    });

    setItems(updatedItems);
  };

  const handleMoveItem = (itemId: number, direction: 'up' | 'down') => {
    const itemIndex = items.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    const newItems = [...items];

    if (direction === 'up' && itemIndex > 0) {
      // Swap with previous item
      [newItems[itemIndex - 1], newItems[itemIndex]] = [newItems[itemIndex], newItems[itemIndex - 1]];
    } else if (direction === 'down' && itemIndex < items.length - 1) {
      // Swap with next item
      [newItems[itemIndex], newItems[itemIndex + 1]] = [newItems[itemIndex + 1], newItems[itemIndex]];
    }

    // Update sort_order for all items
    const updatedItems = newItems.map((item, index) => ({
      ...item,
      sort_order: index
    }));

    setItems(updatedItems);
  };

  const handleCustomerCreated = (customer: Customer) => {
    // Add the new customer to the customers list
    setCustomers([...customers, customer]);

    // Select the new customer
    setFormData({
      ...formData,
      customer_id: customer.id
    });

    // Update the customer search field
    setCustomerSearch(customer.name);
  };

  const calculateTotals = () => {
    // Bereken subtotaal en rond af op 2 decimalen
    const subtotal = Math.round(items.reduce((sum, item) => sum + item.total_price, 0) * 100) / 100;

    // Bereken kortingsbedrag en rond af op 2 decimalen
    const discountAmount = formData.discount_percentage
      ? Math.round((subtotal * (formData.discount_percentage / 100)) * 100) / 100
      : 0;

    // Bereken totaal excl. BTW en rond af op 2 decimalen
    const totalExclVat = Math.round((subtotal - discountAmount) * 100) / 100;

    // Bereken BTW-bedrag en rond af op 2 decimalen
    const vatAmount = formData.vat_percentage
      ? Math.round((totalExclVat * (formData.vat_percentage / 100)) * 100) / 100
      : 0;

    // Bereken totaal incl. BTW en rond af op 2 decimalen
    const totalInclVat = Math.round((totalExclVat + vatAmount) * 100) / 100;

    return {
      subtotal,
      discountAmount,
      totalExclVat,
      vatAmount,
      totalInclVat
    };
  };

  const totals = calculateTotals();

  const handleGenerateDocument = async () => {
    if (!selectedTemplate) {
      setErrors({
        form: 'Selecteer eerst een template'
      });
      return;
    }

    // For new quotations, save first
    if (!isEditing) {
      // Validate required fields
      if (!formData.customer_id || !formData.title) {
        setErrors({
          form: 'Vul eerst klant en titel in voordat je een document genereert'
        });
        return;
      }

      try {
        setSubmitting(true);

        // Save the quotation first
        const quotationData = {
          ...formData,
          items: items
        };

        const savedQuotation = await createQuotation(quotationData);

        // Prepare document data with calculations
        const documentData = {
          ...savedQuotation,
          items: items,
          subtotal: totals.subtotal,
          discount_amount: totals.discountAmount,
          total_excl_vat: totals.totalExclVat,
          vat_amount: totals.vatAmount,
          total_incl_vat: totals.totalInclVat
        };

        const result = await generateQuotationDocument(documentData, selectedTemplate.id);
        navigate(`/documents/${result.id}`);
      } catch (err: any) {
        console.error('Failed to save and generate document:', err);
        setErrors({
          form: err.response?.data?.error || 'Fout bij opslaan en genereren van document'
        });
      } finally {
        setSubmitting(false);
      }
    } else {
      // For existing quotations
      showConfirmation({
        title: 'Offerte document genereren',
        message: `Wil je een DOCX document genereren van deze offerte met template "${selectedTemplate.name}"?`,
        confirmText: 'Genereren',
        cancelText: 'Annuleren',
        onConfirm: async () => {
          try {
            setSubmitting(true);

            // Prepare quotation data with calculations
            const quotationData = {
              ...formData,
              items: items,
              subtotal: totals.subtotal,
              discount_amount: totals.discountAmount,
              total_excl_vat: totals.totalExclVat,
              vat_amount: totals.vatAmount,
              total_incl_vat: totals.totalInclVat
            };

            const result = await generateQuotationDocument(quotationData, selectedTemplate.id);
            navigate(`/documents/${result.id}`);
          } catch (err: any) {
            console.error('Failed to generate document:', err);
            setErrors({
              form: err.response?.data?.error || 'Fout bij genereren van document'
            });
          } finally {
            setSubmitting(false);
          }
        }
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* New Customer Modal */}
      <NewCustomerModal
        isOpen={showNewCustomerModal}
        onClose={() => setShowNewCustomerModal(false)}
        onCustomerCreated={handleCustomerCreated}
      />

      <div className="flex items-center justify-between">
        <button
          onClick={() => navigate('/quotations')}
          className="btn btn-outline btn-sm"
        >
          <FaArrowLeft className="mr-2" /> Terug naar offertes
        </button>

        {/* Document Template Section */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-6 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                📄 Document Generatie
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Genereer een professioneel offerte document met een template
              </p>
            </div>
          </div>

          <div className="space-y-4">
            {/* Template Selection */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Selecteer Template
                </label>
                <select
                  value={selectedTemplate?.id || ''}
                  onChange={(e) => {
                    const templateId = parseInt(e.target.value);
                    const template = templates.find(t => t.id === templateId);
                    setSelectedTemplate(template || null);
                  }}
                  className="input w-full"
                  disabled={submitting}
                >
                  <option value="">Kies een template...</option>
                  {templates.map((template) => (
                    <option key={template.id} value={template.id}>
                      {template.name} {template.is_default ? '(Standaard)' : ''}
                    </option>
                  ))}
                </select>

                {templates.length === 0 && (
                  <p className="text-sm text-amber-600 dark:text-amber-400 mt-2">
                    ⚠️ Geen templates beschikbaar. Ga naar Templates om er een toe te voegen.
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Template Info
                </label>
                <div className="bg-white dark:bg-gray-800 p-3 rounded border text-sm">
                  {selectedTemplate ? (
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {selectedTemplate.name}
                      </p>
                      {selectedTemplate.description && (
                        <p className="text-gray-600 dark:text-gray-400 mt-1">
                          {selectedTemplate.description}
                        </p>
                      )}
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                        Type: {selectedTemplate.file_type?.toUpperCase() || 'DOCX'}
                      </p>
                    </div>
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">
                      Selecteer een template om details te zien
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Action Button */}
            <div className="flex justify-end pt-4 border-t border-blue-200 dark:border-blue-800">
              <button
                onClick={handleGenerateDocument}
                className="btn btn-primary flex items-center"
                disabled={submitting || !selectedTemplate || (!isEditing && (!formData.customer_id || !formData.title))}
              >
                {submitting ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    {isEditing ? 'Genereren...' : 'Opslaan & Genereren...'}
                  </>
                ) : (
                  <>
                    <FaFileAlt className="mr-2" />
                    {isEditing ? 'Document Genereren' : 'Opslaan & Document Genereren'}
                  </>
                )}
              </button>
            </div>

            {/* Help Text */}
            {!isEditing && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-3 rounded text-sm">
                <p className="text-yellow-800 dark:text-yellow-200">
                  💡 <strong>Tip:</strong> Vul eerst de klant en titel in voordat je een document genereert.
                  De offerte wordt automatisch opgeslagen voordat het document wordt gegenereerd.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {errors.form && (
          <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 p-3 rounded-md">
            {errors.form}
          </div>
        )}

        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4">
            Offerte gegevens
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="form-group">
              <label htmlFor="customer_id" className="form-label">
                Klant <span className="text-red-500">*</span>
              </label>
              <div className="relative customer-search-container">
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                  <div className="relative flex-grow">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                      </svg>
                    </div>
                    <input
                      type="text"
                      placeholder="Zoek klant op naam..."
                      value={customerSearch}
                      onChange={(e) => {
                        setCustomerSearch(e.target.value);
                        if (e.target.value.trim() !== '') {
                          setShowCustomerDropdown(true);
                        }
                      }}
                      onFocus={() => {
                        if (customerSearch.trim() !== '') {
                          setShowCustomerDropdown(true);
                        }
                      }}
                      className={`input w-full text-sm pl-10 ${errors.customer_id ? 'border-red-500' : ''}`}
                      disabled={submitting}
                    />
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      className="btn btn-secondary whitespace-nowrap browse-customers-btn text-xs sm:text-sm flex items-center justify-center"
                      onClick={async () => {
                        if (!showCustomerDropdown) {
                          setIsSearching(true);
                          try {
                            const response = await getAllCustomersNoPage();
                            setSearchResults(response.customers);
                          } catch (error) {
                            console.error('Error fetching all customers:', error);
                          } finally {
                            setIsSearching(false);
                          }
                        }
                        setShowCustomerDropdown(!showCustomerDropdown);
                      }}
                      disabled={submitting}
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                      Alle Klanten
                    </button>

                    <button
                      type="button"
                      className="btn btn-primary whitespace-nowrap text-xs sm:text-sm flex items-center justify-center"
                      onClick={() => setShowNewCustomerModal(true)}
                      disabled={submitting}
                    >
                      <FaUserPlus className="mr-1" />
                      Nieuwe Klant
                    </button>
                  </div>
                </div>
                {showCustomerDropdown && searchResults.length > 0 && (
                  <div className="absolute z-10 mt-1 w-full bg-white dark:bg-dark-secondary border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                    {isSearching ? (
                      <div className="p-4 text-center text-gray-500 dark:text-gray-400 flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-amspm-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Zoeken...
                      </div>
                    ) : (
                      <>
                        {searchResults.map((customer) => (
                          <div
                            key={customer.id}
                            className={`px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150 ${formData.customer_id === customer.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                            onClick={() => {
                              setFormData({
                                ...formData,
                                customer_id: customer.id
                              });
                              setCustomerSearch(customer.name);
                              setShowCustomerDropdown(false);
                            }}
                          >
                            <div className="font-medium text-amspm-text dark:text-dark-text">{customer.name}</div>
                            {customer.address && (
                              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{customer.address}</div>
                            )}
                          </div>
                        ))}

                        {searchResults.length === 0 && customerSearch.trim().length >= 2 && (
                          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                            Geen klanten gevonden die overeenkomen met "{customerSearch}"
                          </div>
                        )}
                      </>
                    )}
                  </div>
                )}
              </div>
              {formData.customer_id && (
                <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-md">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium text-blue-800 dark:text-blue-300 text-sm uppercase">Geselecteerde Klant:</p>
                      <p className="text-gray-800 dark:text-gray-200 font-medium mt-1">{customers.find(c => c.id === formData.customer_id)?.name || 'Onbekend'}</p>
                      {customers.find(c => c.id === formData.customer_id)?.address && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{customers.find(c => c.id === formData.customer_id)?.address}</p>
                      )}
                    </div>
                    <button
                      type="button"
                      className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors duration-150 flex items-center"
                      onClick={() => {
                        setFormData({
                          ...formData,
                          customer_id: undefined
                        });
                        setCustomerSearch('');
                      }}
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                      Wissen
                    </button>
                  </div>
                </div>
              )}
              {errors.customer_id && (
                <p className="text-red-500 text-sm mt-1">{errors.customer_id}</p>
              )}
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Zoek een klant op naam of bekijk alle klanten.
              </p>
            </div>

            <div className="form-group">
              <label htmlFor="title" className="form-label">
                Titel <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title || ''}
                onChange={handleChange}
                className={`input ${errors.title ? 'border-red-500' : ''}`}
                required
                disabled={submitting}
              />
              {errors.title && (
                <p className="text-red-500 text-sm mt-1">{errors.title}</p>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="valid_until" className="form-label">
                Geldig tot
              </label>
              <input
                type="datetime-local"
                id="valid_until"
                name="valid_until"
                value={formData.valid_until || ''}
                onChange={handleChange}
                className={`input ${errors.valid_until ? 'border-red-500' : ''}`}
                disabled={submitting}
              />
              {errors.valid_until && (
                <p className="text-red-500 text-sm mt-1">{errors.valid_until}</p>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="status" className="form-label">
                Status
              </label>
              <div className="flex flex-col space-y-2">
                <div className="flex flex-wrap gap-2">
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, status: 'concept' })}
                    className={`px-4 py-2 rounded-md flex items-center ${
                      formData.status === 'concept'
                        ? 'bg-gray-200 dark:bg-gray-700 font-medium'
                        : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                    }`}
                  >
                    <span className="w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                    Concept
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, status: 'sent' })}
                    className={`px-4 py-2 rounded-md flex items-center ${
                      formData.status === 'sent'
                        ? 'bg-blue-200 dark:bg-blue-900/50 font-medium'
                        : 'bg-blue-100 dark:bg-blue-900/20 hover:bg-blue-200 dark:hover:bg-blue-900/40'
                    }`}
                  >
                    <span className="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                    Verzonden
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, status: 'accepted' })}
                    className={`px-4 py-2 rounded-md flex items-center ${
                      formData.status === 'accepted'
                        ? 'bg-green-200 dark:bg-green-900/50 font-medium'
                        : 'bg-green-100 dark:bg-green-900/20 hover:bg-green-200 dark:hover:bg-green-900/40'
                    }`}
                  >
                    <span className="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                    Geaccepteerd
                  </button>
                  <button
                    type="button"
                    onClick={() => setFormData({ ...formData, status: 'rejected' })}
                    className={`px-4 py-2 rounded-md flex items-center ${
                      formData.status === 'rejected'
                        ? 'bg-red-200 dark:bg-red-900/50 font-medium'
                        : 'bg-red-100 dark:bg-red-900/20 hover:bg-red-200 dark:hover:bg-red-900/40'
                    }`}
                  >
                    <span className="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                    Geweigerd
                  </button>
                </div>

                {/* Hidden select for form submission */}
                <select
                  id="status"
                  name="status"
                  value={formData.status || 'concept'}
                  onChange={handleChange}
                  className="hidden"
                  disabled={submitting}
                >
                  <option value="concept">Concept</option>
                  <option value="sent">Verzonden</option>
                  <option value="accepted">Geaccepteerd</option>
                  <option value="rejected">Afgewezen</option>
                </select>
              </div>

              {formData.status === 'rejected' && (
                <div className="mt-2 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-700 dark:text-red-400 text-sm">
                  <strong>Let op:</strong> Bij afgewezen offertes worden de klant en offerte automatisch verwijderd door een administrator.
                </div>
              )}

              {formData.status === 'accepted' && (
                <div className="mt-2 p-3 bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md text-green-700 dark:text-green-400 text-sm">
                  <strong>Geaccepteerd:</strong> Deze offerte is geaccepteerd door de klant.
                </div>
              )}
            </div>

            <div className="form-group md:col-span-2">
              <label htmlFor="introduction" className="form-label">
                Inleiding
              </label>
              <textarea
                id="introduction"
                name="introduction"
                value={formData.introduction || ''}
                onChange={handleChange}
                className="input min-h-[100px]"
                disabled={submitting}
              />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">
              Producten
            </h2>

            <div className="w-1/2">
              <ProductSelector onProductSelected={handleProductSelected} />
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-md p-4 mb-4">
            <div className="flex items-start">
              <div className="text-blue-500 dark:text-blue-400 mt-1 mr-3 flex-shrink-0">ℹ️</div>
              <div>
                <h3 className="font-medium text-blue-800 dark:text-blue-300">Arbeid toevoegen</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Bij elk product kunt u de benodigde arbeidstijd selecteren. Arbeid wordt berekend tegen een tarief van €{LABOR_HOUR_RATE} per uur.
                </p>
                <ul className="text-sm text-gray-600 dark:text-gray-400 mt-2 list-disc pl-5 space-y-1">
                  <li>Arbeidsitems worden automatisch toegevoegd aan producten</li>
                  <li>Arbeidsitems kunnen niet handmatig worden bewerkt</li>
                  <li>Als u een product verwijdert, wordt het bijbehorende arbeidsitem ook verwijderd</li>
                  <li>Als u de hoeveelheid van een product wijzigt, wordt de hoeveelheid van het arbeidsitem automatisch aangepast</li>
                </ul>
              </div>
            </div>
          </div>



          <QuotationItemList
            items={items}
            onDeleteItem={handleDeleteItem}
            onUpdateItem={handleUpdateItem}
            onMoveItem={handleMoveItem}
          />

          <div className="mt-6 flex justify-end">
            <div className="w-64 space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-dark-text-light">Subtotaal:</span>
                <span className="text-amspm-text dark:text-dark-text">€ {totals.subtotal.toFixed(2)}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-gray-600 dark:text-dark-text-light mr-2">Korting:</span>
                  <input
                    type="number"
                    name="discount_percentage"
                    value={formData.discount_percentage || ''}
                    onChange={handleChange}
                    className="input w-16 text-right py-1 px-2 text-sm"
                    min="0"
                    max="100"
                    step="0.1"
                    disabled={submitting}
                  />
                  <span className="ml-1">%</span>
                </div>
                <span className="text-amspm-text dark:text-dark-text">€ {totals.discountAmount.toFixed(2)}</span>
              </div>

              <div className="flex justify-between font-medium">
                <span className="text-gray-600 dark:text-dark-text-light">Totaal excl. BTW:</span>
                <span className="text-amspm-text dark:text-dark-text">€ {totals.totalExclVat.toFixed(2)}</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-gray-600 dark:text-dark-text-light mr-2">BTW:</span>
                  <input
                    type="number"
                    name="vat_percentage"
                    value={formData.vat_percentage || ''}
                    onChange={handleChange}
                    className="input w-16 text-right py-1 px-2 text-sm"
                    min="0"
                    step="0.1"
                    disabled={submitting}
                  />
                  <span className="ml-1">%</span>
                </div>
                <span className="text-amspm-text dark:text-dark-text">€ {totals.vatAmount.toFixed(2)}</span>
              </div>

              <div className="flex justify-between text-lg font-bold pt-2 border-t border-gray-200 dark:border-gray-700">
                <span className="text-amspm-text dark:text-dark-text">Totaal incl. BTW:</span>
                <span className="text-amspm-primary dark:text-dark-accent">€ {totals.totalInclVat.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4">
            Afsluiting
          </h2>

          <div className="form-group">
            <label htmlFor="conclusion" className="form-label">
              Afsluiting
            </label>
            <textarea
              id="conclusion"
              name="conclusion"
              value={formData.conclusion || ''}
              onChange={handleChange}
              className="input min-h-[100px]"
              disabled={submitting}
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => navigate('/quotations')}
            className="btn btn-outline"
            disabled={submitting}
          >
            <FaTimes className="mr-2" /> Annuleren
          </button>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={submitting}
          >
            {submitting ? (
              <>
                <LoadingSpinner size="sm" /> Opslaan...
              </>
            ) : (
              <>
                <FaSave className="mr-2" /> Opslaan
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default QuotationForm;
