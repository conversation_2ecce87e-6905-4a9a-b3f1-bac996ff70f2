import React, { useState, useEffect } from 'react';
import { Quotation } from '../../types/quotation';
import { getAllQuotations, searchQuotations, deleteQuotation, updateQuotationStatus, rejectAndDeleteQuotation } from '../../services/quotationService';
import { useConfirmation } from '../../context/ConfirmationContext';
import { FaSearch, FaEdit, FaTrash, FaPlus, FaFileAlt, FaCheck, FaTimes, FaCalendarPlus } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';
import Pagination from '../Pagination';
import { Link } from 'react-router-dom';

import CreateUpcomingEventModal from '../CreateUpcomingEventModal';
import { Event } from '../../types/event';
import { MobileCard, MobileSearch, MobileButtonGroup } from '../common/MobileUtils';
import { useMobile } from '../../hooks/useMobile';

const QuotationList: React.FC = () => {
  const [quotations, setQuotations] = useState<Quotation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [perPage, setPerPage] = useState(20);
  const [selectedQuotation, setSelectedQuotation] = useState<Quotation | null>(null);
  const [quotationsWithEvents, setQuotationsWithEvents] = useState<Record<number, Event[]>>({});
  const { showConfirmation } = useConfirmation();
  const { isMobile } = useMobile();

  useEffect(() => {
    fetchQuotations();
  }, [page, perPage]);

  const fetchQuotations = async () => {
    try {
      setLoading(true);
      setError(null);

      let response;
      if (searchTerm) {
        response = await searchQuotations(searchTerm, page, perPage);
      } else {
        response = await getAllQuotations(page, perPage);
      }

      setQuotations(response.quotations);
      setTotalItems(response.total);
      setTotalPages(Math.ceil(response.total / perPage));

      // Haal installatie-events op voor geaccepteerde offertes
      const acceptedQuotations = response.quotations.filter(q => q.status === 'accepted');
      if (acceptedQuotations.length > 0) {
        await fetchInstallationEventsForQuotations(acceptedQuotations);
      }
    } catch (err: any) {
      console.error('Failed to fetch quotations:', err);
      setError(err.response?.data?.error || 'Fout bij ophalen van offertes');
    } finally {
      setLoading(false);
    }
  };

  const fetchInstallationEventsForQuotations = async (quotations: Quotation[]) => {
    const eventsMap: Record<number, Event[]> = {};

    console.log('Fetching installation events for quotations:', quotations.map(q => q.id));

    // Haal events op voor elke offerte met een document_id
    await Promise.all(
      quotations.map(async (quotation) => {
        try {
          if (quotation.document_id) {
            // Haal events op die gekoppeld zijn aan het document
            const response = await fetch(`/api/events?document_id=${quotation.document_id}&event_type=checklist oplevering installatie`);
            const data = await response.json();
            console.log(`Received events for quotation ${quotation.id} (document ${quotation.document_id}):`, data.events);
            eventsMap[quotation.id] = data.events || [];
          } else {
            eventsMap[quotation.id] = [];
          }
        } catch (err) {
          console.error(`Failed to fetch events for quotation ${quotation.id}:`, err);
          eventsMap[quotation.id] = [];
        }
      })
    );

    console.log('Setting quotations with events:', eventsMap);
    setQuotationsWithEvents(eventsMap);
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchQuotations();
  };

  const handleDelete = (quotation: Quotation) => {
    showConfirmation({
      title: 'Offerte verwijderen',
      message: `Weet je zeker dat je de offerte "${quotation.title}" wilt verwijderen?`,
      confirmText: 'Verwijderen',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          await deleteQuotation(quotation.id);
          fetchQuotations();
        } catch (err: any) {
          console.error('Failed to delete quotation:', err);
          setError(err.response?.data?.error || 'Fout bij verwijderen van offerte');
        }
      }
    });
  };

  const handleAccept = (quotation: Quotation, e: React.MouseEvent) => {
    e.stopPropagation(); // Voorkom dat de rij-klik wordt geactiveerd

    showConfirmation({
      title: 'Offerte accepteren',
      message: `Weet je zeker dat je de offerte "${quotation.title}" wilt accepteren?`,
      confirmText: 'Accepteren',
      cancelText: 'Annuleren',
      confirmButtonClass: 'bg-green-600 hover:bg-green-700',
      onConfirm: async () => {
        try {
          console.log('Accepting quotation:', quotation.id);
          await updateQuotationStatus(quotation.id, 'accepted');
          await fetchQuotations();

          // Ask if user wants to schedule an installation
          showConfirmation({
            title: 'Installatie inplannen',
            message: 'Wil je nu een installatie inplannen voor deze geaccepteerde offerte?',
            confirmText: 'Ja, plan installatie',
            cancelText: 'Nee, later',
            confirmButtonClass: 'bg-blue-600 hover:bg-blue-700',
            onConfirm: () => {
              // Toon de installatie modal
              setSelectedQuotation(quotation);
            }
          });
        } catch (err: any) {
          console.error('Failed to accept quotation:', err);
          setError(err.response?.data?.error || 'Fout bij accepteren van offerte');
        }
      }
    });
  };

  const handleReject = (quotation: Quotation, e: React.MouseEvent) => {
    e.stopPropagation(); // Voorkom dat de rij-klik wordt geactiveerd

    showConfirmation({
      title: 'Offerte afwijzen',
      message: `Weet je zeker dat je de offerte "${quotation.title}" wilt afwijzen? De offerte en mogelijk de klant worden direct verwijderd.`,
      confirmText: 'Afwijzen en verwijderen',
      cancelText: 'Annuleren',
      confirmButtonClass: 'bg-red-600 hover:bg-red-700',
      onConfirm: async () => {
        try {
          console.log('Rejecting quotation:', quotation.id);
          const result = await rejectAndDeleteQuotation(quotation.id);
          console.log('Rejection result:', result);

          // Show success message
          showConfirmation({
            title: 'Offerte afgewezen',
            message: `De offerte is succesvol afgewezen en verwijderd. ${
              result.customer_deleted ? 'De klant is ook verwijderd omdat er geen andere offertes waren.' : ''
            }`,
            confirmText: 'OK',
            showCancel: false,
            confirmButtonClass: 'bg-blue-600 hover:bg-blue-700',
            onConfirm: () => {
              fetchQuotations();
            }
          });
        } catch (err: any) {
          console.error('Failed to reject and delete quotation:', err);
          setError(err.response?.data?.error || 'Fout bij afwijzen en verwijderen van offerte');
        }
      }
    });
  };

  const handleRowClick = (quotationId: number) => {
    window.location.href = `/quotations/${quotationId}`;
  };

  const handleEventCreated = () => {
    // Vernieuw de offertes na het aanmaken van een gebeurtenis
    fetchQuotations();
  };

  const hasInstallationEvent = (quotationId: number): boolean => {
    // Controleer of er al een installatie-event is voor deze offerte
    return quotationsWithEvents[quotationId]?.length > 0;
  };

  const handlePlanInstallation = (quotation: Quotation, e: React.MouseEvent) => {
    e.stopPropagation(); // Voorkom dat de rij-klik wordt geactiveerd
    setSelectedQuotation(quotation);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'concept':
        return <span className="badge badge-gray">Concept</span>;
      case 'sent':
        return <span className="badge badge-blue">Verzonden</span>;
      case 'accepted':
        return <span className="badge badge-green">Geaccepteerd</span>;
      case 'rejected':
        return <span className="badge badge-red">Afgewezen</span>;
      default:
        return <span className="badge badge-gray">{status}</span>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <input
              type="text"
              placeholder="Zoek op titel, offertenummer of klantnaam..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input w-full pl-10"
            />
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </form>

        <div className="flex gap-2">
          <Link to="/quotations/templates" className="btn btn-secondary">
            <FaFileAlt className="mr-2" /> Templates
          </Link>
          <Link to="/quotations/new" className="btn btn-primary">
            <FaPlus className="mr-2" /> Nieuwe offerte
          </Link>
        </div>
      </div>



      {error && (
        <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 p-3 rounded-md">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : quotations.length === 0 ? (
        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">
            {searchTerm ? 'Geen offertes gevonden voor deze zoekopdracht.' : 'Geen offertes beschikbaar.'}
          </p>
        </div>
      ) : (
        <>
          {/* Desktop Table View */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full bg-white dark:bg-dark-card shadow-md rounded-lg">
              <thead className="bg-gray-50 dark:bg-dark-secondary">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Offertenummer
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Titel
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Klant
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Totaal (excl. BTW)
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Geldig tot
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                    Acties
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {quotations.map((quotation) => (
                  <tr
                    key={quotation.id}
                    className="hover:bg-gray-50 dark:hover:bg-dark-secondary cursor-pointer"
                    onClick={() => handleRowClick(quotation.id)}
                  >
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-amspm-text dark:text-dark-text">
                      {quotation.quotation_number || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm text-amspm-text dark:text-dark-text">
                      {quotation.title}
                    </td>
                    <td className="px-4 py-3 text-sm text-amspm-text dark:text-dark-text">
                      {quotation.customer_name || '-'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      {getStatusBadge(quotation.status)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-amspm-text dark:text-dark-text">
                      € {quotation.total_excl_vat.toFixed(2)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-amspm-text dark:text-dark-text">
                      {quotation.valid_until ? new Date(quotation.valid_until).toLocaleDateString() : '-'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-right" onClick={(e) => e.stopPropagation()}>
                      <div className="flex items-center justify-end space-x-2">
                        {quotation.status !== 'accepted' && quotation.status !== 'rejected' && (
                          <>
                            <button
                              onClick={(e) => handleAccept(quotation, e)}
                              className="btn btn-sm bg-green-600 hover:bg-green-700 text-white border-none"
                              title="Accepteren"
                            >
                              <FaCheck className="mr-1" /> Geaccepteerd
                            </button>
                            <button
                              onClick={(e) => handleReject(quotation, e)}
                              className="btn btn-sm bg-red-600 hover:bg-red-700 text-white border-none"
                              title="Afwijzen"
                            >
                              <FaTimes className="mr-1" /> Geweigerd
                            </button>
                          </>
                        )}

                        {/* Toon knop voor inplannen installatie als de offerte geaccepteerd is en er nog geen installatie-event is */}
                        {quotation.status === 'accepted' && !hasInstallationEvent(quotation.id) && (
                          <button
                            onClick={(e) => handlePlanInstallation(quotation, e)}
                            className="btn btn-sm bg-blue-600 hover:bg-blue-700 text-white border-none"
                            title="Installatie inplannen"
                          >
                            <FaCalendarPlus className="mr-1" /> Installatie
                          </button>
                        )}

                        {/* Toon informatie over geplande installatie als er al een installatie-event is */}
                        {quotation.status === 'accepted' && hasInstallationEvent(quotation.id) && (
                          <span className="text-xs text-green-600 dark:text-green-400 ml-2">
                            Installatie gepland
                          </span>
                        )}
                        <div className="flex items-center space-x-1">
                          <Link
                            to={`/quotations/${quotation.id}`}
                            className="p-1.5 rounded-full bg-gray-100 dark:bg-dark-secondary text-amspm-primary dark:text-dark-accent hover:bg-gray-200 dark:hover:bg-dark-hover"
                            title="Bekijken"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <FaFileAlt />
                          </Link>
                          <Link
                            to={`/quotations/${quotation.id}/edit`}
                            className="p-1.5 rounded-full bg-gray-100 dark:bg-dark-secondary text-amspm-primary dark:text-dark-accent hover:bg-gray-200 dark:hover:bg-dark-hover"
                            title="Bewerken"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <FaEdit />
                          </Link>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(quotation);
                            }}
                            className="p-1.5 rounded-full bg-gray-100 dark:bg-dark-secondary text-red-500 hover:bg-gray-200 dark:hover:bg-dark-hover"
                            title="Verwijderen"
                          >
                            <FaTrash />
                          </button>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Card View */}
          <div className="md:hidden space-y-4">
            {quotations.map((quotation) => (
              <MobileCard
                key={quotation.id}
                onClick={() => handleRowClick(quotation.id)}
                hoverable={true}
              >
                <div className="mobile-card-header">
                  <div className="flex-1">
                    <h3 className="mobile-card-title">{quotation.title}</h3>
                    <p className="text-sm text-gray-500 dark:text-dark-text-light">
                      {quotation.quotation_number || 'Geen nummer'}
                    </p>
                  </div>
                  <div className="text-right">
                    {getStatusBadge(quotation.status)}
                  </div>
                </div>

                <div className="mobile-card-content">
                  <div className="mobile-card-row">
                    <span className="mobile-card-label">Klant:</span>
                    <span className="mobile-card-value">{quotation.customer_name || '-'}</span>
                  </div>

                  <div className="mobile-card-row">
                    <span className="mobile-card-label">Totaal (excl. BTW):</span>
                    <span className="mobile-card-value font-medium text-amspm-primary dark:text-dark-accent">
                      € {quotation.total_excl_vat.toFixed(2)}
                    </span>
                  </div>

                  <div className="mobile-card-row">
                    <span className="mobile-card-label">Geldig tot:</span>
                    <span className="mobile-card-value">
                      {quotation.valid_until ? new Date(quotation.valid_until).toLocaleDateString() : '-'}
                    </span>
                  </div>

                  {quotation.status === 'accepted' && hasInstallationEvent(quotation.id) && (
                    <div className="mobile-card-row">
                      <span className="text-xs text-green-600 dark:text-green-400">
                        ✓ Installatie gepland
                      </span>
                    </div>
                  )}
                </div>

                <div className="mobile-card-actions" onClick={(e) => e.stopPropagation()}>
                  {quotation.status !== 'accepted' && quotation.status !== 'rejected' && (
                    <MobileButtonGroup direction="vertical">
                      <button
                        onClick={(e) => handleAccept(quotation, e)}
                        className="btn bg-green-600 hover:bg-green-700 text-white border-none mobile-touch-target"
                      >
                        <FaCheck className="mr-2" />
                        Accepteren
                      </button>
                      <button
                        onClick={(e) => handleReject(quotation, e)}
                        className="btn bg-red-600 hover:bg-red-700 text-white border-none mobile-touch-target"
                      >
                        <FaTimes className="mr-2" />
                        Afwijzen
                      </button>
                    </MobileButtonGroup>
                  )}

                  {quotation.status === 'accepted' && !hasInstallationEvent(quotation.id) && (
                    <button
                      onClick={(e) => handlePlanInstallation(quotation, e)}
                      className="btn bg-blue-600 hover:bg-blue-700 text-white border-none w-full mobile-touch-target"
                    >
                      <FaCalendarPlus className="mr-2" />
                      Installatie inplannen
                    </button>
                  )}

                  <MobileButtonGroup direction="horizontal">
                    <Link
                      to={`/quotations/${quotation.id}`}
                      className="btn btn-outline flex items-center justify-center mobile-touch-target"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <FaFileAlt className="mr-2" />
                      Bekijken
                    </Link>
                    <Link
                      to={`/quotations/${quotation.id}/edit`}
                      className="btn btn-outline flex items-center justify-center mobile-touch-target"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <FaEdit className="mr-2" />
                      Bewerken
                    </Link>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(quotation);
                      }}
                      className="btn btn-danger flex items-center justify-center mobile-touch-target"
                    >
                      <FaTrash className="mr-2" />
                      Verwijderen
                    </button>
                  </MobileButtonGroup>
                </div>
              </MobileCard>
            ))}
          </div>

          <Pagination
            currentPage={page}
            totalPages={totalPages}
            onPageChange={setPage}
            totalItems={totalItems}
            itemsPerPage={perPage}
            onItemsPerPageChange={(newPerPage) => {
              setPerPage(newPerPage);
              setPage(1); // Reset to first page when changing items per page
              fetchQuotations();
            }}
          />
        </>
      )}

      {/* Installatie modal */}
      {selectedQuotation && selectedQuotation.document_id && (
        <CreateUpcomingEventModal
          document={{
            id: selectedQuotation.document_id,
            customer_id: selectedQuotation.customer_id,
            document_type: "offerte",
            // Andere vereiste velden
            event_id: null,
            file_url: "",
            uploaded_by: "0",
            expiry_date: null,
            created_at: "",
            expiry_status: "green",
            status: "active",
            sub_documents: []
          }}
          onClose={() => setSelectedQuotation(null)}
          onEventCreated={handleEventCreated}
        />
      )}
    </div>
  );
};

export default QuotationList;
