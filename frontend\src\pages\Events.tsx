import React, { useState, useEffect } from "react";
import { getAllEvents, createEvent, updateEvent, deleteEvent } from "../services/eventService";
import { getAllUsers } from "../services/userService";
import { getAllCustomers } from "../services/customerService";
import { Event } from "../types/event";
import { User } from "../types/user";
import { Customer } from "../types/customer";
import LoadingSpinner from '../components/LoadingSpinner';
import EventModal from '../components/EventModal';
import Pagination from '../components/Pagination';
import { useConfirmation } from '../context/ConfirmationContext';
import { FaCalendarAlt, FaPlus, FaClock, FaCheckCircle } from "react-icons/fa";

interface NewEvent {
  customer_id: number | null;
  customer_name?: string | null;
  customer_address?: string | null;
  event_type: string;
  description: string;
  scheduled_date: string;
  user_ids: number[];
  user_id: number | null; // Keep for backward compatibility
}

const Events: React.FC = () => {
  const { showConfirmation } = useConfirmation();
  const [events, setEvents] = useState<Event[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [newEvent, setNewEvent] = useState<NewEvent>({
    customer_id: null,
    customer_name: null,
    customer_address: null,
    event_type: "",
    description: "",
    scheduled_date: "",
    user_ids: [] as number[],
    user_id: null // Keep for backward compatibility
  });
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [totalItems, setTotalItems] = useState(0);

  const fetchEvents = async (page: number, perPage: number) => {
    try {
      const response = await getAllEvents(page, perPage);
      setEvents(response.events);
      setTotalItems(response.total);
      setLoading(false);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch events");
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await getAllUsers();
      setUsers(response.users);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch users");
    }
  };

  const fetchCustomers = async () => {
    try {
      const response = await getAllCustomers();
      setCustomers(response.customers);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to fetch customers");
    }
  };

  useEffect(() => {
    fetchEvents(currentPage, itemsPerPage);
    fetchUsers();
    fetchCustomers();
  }, [currentPage, itemsPerPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (perPage: number) => {
    setItemsPerPage(perPage);
    setCurrentPage(1);
  };

  const handleCreate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    try {
      const formattedDate = new Date(newEvent.scheduled_date).toISOString();
      const event = await createEvent(
        newEvent.customer_id || null,  // Ensure null if falsy
        newEvent.event_type,
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : null  // Use user_ids array
      );
      setEvents([...events, event]);
      setShowModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to create event");
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingEvent) return;
    setSubmitting(true);
    try {
      const formattedDate = new Date(newEvent.scheduled_date).toISOString();
      const event = await updateEvent(
        editingEvent.id,
        newEvent.customer_id !== null ? newEvent.customer_id : 0,
        newEvent.event_type,
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : undefined
      );
      setEvents(events.map((e) => (e.id === editingEvent.id ? event : e)));
      setEditingEvent(null);
      setNewEvent({
        customer_id: null,
        customer_name: null,
        customer_address: null,
        event_type: "",
        description: "",
        scheduled_date: "",
        user_ids: [],
        user_id: null
      });
      setShowModal(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || "Failed to update event");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (eventId: number) => {
    const event = events.find(e => e.id === eventId);
    if (!event) return;

    showConfirmation({
      title: "Delete Event",
      message: `Are you sure you want to delete this ${event.event_type} event for ${event.customer_name}? This action cannot be undone.`,
      confirmText: "Delete",
      confirmButtonClass: "bg-red-600 hover:bg-red-700",
      onConfirm: async () => {
        setSubmitting(true);
        try {
          await deleteEvent(eventId);
          setEvents(events.filter((e) => e.id !== eventId));
          setError(null);
        } catch (err: any) {
          setError(err.response?.data?.error || "Failed to delete event");
        } finally {
          setSubmitting(false);
        }
      }
    });
  };

  // Filter events by status
  const pendingEvents = events.filter((event) => event.status.toLowerCase() === "pending");
  const completedEvents = events.filter((event) => event.status.toLowerCase() === "completed");

  // Reusable component for rendering events
  const renderEventTable = (eventList: Event[], title: string) => (
    <div className="mb-6 sm:mb-12">
      <h2 className="text-xl sm:text-2xl font-bold text-amspm-text mb-2 sm:mb-4">{title}</h2>

      {/* Desktop view - Table */}
      <div className="hidden md:block bg-white dark:bg-dark-secondary rounded-lg shadow overflow-hidden overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr>
              <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                Customer
              </th>
              <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                Scheduled Date
              </th>
              <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                Completed By
              </th>
              <th className="px-6 py-3 border-b border-gray-200 dark:border-dark-border bg-gray-50 dark:bg-dark-tertiary text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-dark-secondary divide-y divide-gray-200 dark:divide-dark-border">
            {eventList.length > 0 ? (
              eventList.map((event) => (
                <tr key={event.id} className="hover:bg-gray-50 dark:hover:bg-dark-hover">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text">
                    {event.customer_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text">
                    {event.event_type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text">
                    {new Date(event.scheduled_date).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      event.status === 'completed'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    }`}>
                      {event.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text">
                    {event.status === 'completed' && event.completed_by_name ? event.completed_by_name : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text">
                    <div className="flex space-x-2">
                      {event.status !== "completed" && (
                        <button
                          onClick={() => {
                            setEditingEvent(event);
                            setNewEvent({
                              customer_id: event.customer_id ?? null,
                              customer_name: event.customer_name,
                              customer_address: event.customer_address,
                              event_type: event.event_type,
                              description: event.description,
                              scheduled_date: event.scheduled_date.slice(0, 16),
                              user_ids: event.user_ids || [],
                              user_id: event.user_id ?? null,
                            });
                            setShowModal(true);
                          }}
                          className="btn btn-secondary"
                        >
                          Edit
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(event.id)}
                        className="btn btn-danger"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-dark-text text-center">
                  No {title.toLowerCase()} found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile view - Cards */}
      <div className="md:hidden space-y-4">
        {eventList.length > 0 ? (
          eventList.map((event) => (
            <div key={event.id} className="bg-white dark:bg-dark-secondary rounded-lg shadow p-4">
              <div className="flex justify-between items-start mb-2">
                <div>
                  <h3 className="font-bold text-amspm-primary dark:text-dark-accent">{event.customer_name}</h3>
                  <p className="text-sm text-gray-600 dark:text-dark-text-light">{event.event_type}</p>
                </div>
                <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  event.status === 'completed'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                }`}>
                  {event.status}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                <div>
                  <p className="text-gray-500 dark:text-dark-text-light font-medium">Scheduled:</p>
                  <p className="dark:text-dark-text">{new Date(event.scheduled_date).toLocaleString()}</p>
                </div>
                {event.completed_at && (
                  <div>
                    <p className="text-gray-500 dark:text-dark-text-light font-medium">Completed:</p>
                    <p className="dark:text-dark-text">{new Date(event.completed_at).toLocaleString()}</p>
                  </div>
                )}
                {event.status === 'completed' && event.completed_by_name && (
                  <div>
                    <p className="text-gray-500 dark:text-dark-text-light font-medium">Completed by:</p>
                    <p className="dark:text-dark-text">{event.completed_by_name}</p>
                  </div>
                )}
                <div>
                  <p className="text-gray-500 dark:text-dark-text-light font-medium">Assigned to:</p>
                  <p className="dark:text-dark-text">
                    {event.user_names && event.user_names.length > 0
                      ? event.user_names.join(', ')
                      : event.user_email || 'None'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-dark-text-light font-medium">Address:</p>
                  <p className="truncate dark:text-dark-text">{event.customer_address}</p>
                </div>
              </div>

              {event.description && (
                <div className="mb-3">
                  <p className="text-gray-500 dark:text-dark-text-light font-medium text-sm">Description:</p>
                  <p className="text-sm dark:text-dark-text">{event.description}</p>
                </div>
              )}

              <div className="flex space-x-2 mt-3 pt-3 border-t border-gray-200 dark:border-dark-border">
                {event.status !== "completed" && (
                  <button
                    onClick={() => {
                      setEditingEvent(event);
                      setNewEvent({
                        customer_id: event.customer_id ?? null,
                        customer_name: event.customer_name,
                        customer_address: event.customer_address,
                        event_type: event.event_type,
                        description: event.description,
                        scheduled_date: event.scheduled_date.slice(0, 16),
                        user_ids: event.user_ids || [],
                        user_id: event.user_id ?? null,
                      });
                      setShowModal(true);
                    }}
                    className="btn btn-secondary text-xs flex-1"
                  >
                    Edit
                  </button>
                )}
                <button
                  onClick={() => handleDelete(event.id)}
                  className="btn btn-danger text-xs flex-1"
                >
                  Delete
                </button>
              </div>
            </div>
          ))
        ) : (
          <div className="bg-white dark:bg-dark-secondary rounded-lg shadow p-4 text-center text-gray-500 dark:text-dark-text-light">
            No {title.toLowerCase()} found.
          </div>
        )}
      </div>
    </div>
  );

  if (loading) {
    return <LoadingSpinner message="Loading events..." />;
  }

  const handleModalClose = () => {
    setShowModal(false);
    setEditingEvent(null);
    setNewEvent({
      customer_id: null,
      customer_name: null,
      customer_address: null,
      event_type: "",
      description: "",
      scheduled_date: "",
      user_ids: [],
      user_id: null
    });
  };

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-8 space-y-3 sm:space-y-0">
        <div className="flex items-center">
          <FaCalendarAlt className="text-amspm-primary mr-2 sm:mr-3 hidden sm:block" size={30} />
          <FaCalendarAlt className="text-amspm-primary mr-2 sm:mr-3 sm:hidden" size={24} />
          <h1 className="text-2xl sm:text-3xl font-bold text-amspm-text">Events Management</h1>
        </div>
        <button
          onClick={() => {
            setNewEvent({
              customer_id: null,
              customer_name: null,
              customer_address: null,
              event_type: "",
              description: "",
              scheduled_date: "",
              user_ids: [],
              user_id: null
            });
            setEditingEvent(null);
            setShowModal(true);
          }}
          className="btn btn-secondary flex items-center text-sm sm:text-base w-full sm:w-auto"
        >
          <FaPlus className="mr-2" /> Create Event
        </button>
      </div>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      {showModal && (
        <EventModal
          event={newEvent}
          onClose={handleModalClose}
          onSubmit={editingEvent ? handleUpdate : handleCreate}
          setEvent={setNewEvent}
          isEditing={!!editingEvent}
          submitting={submitting}
          customers={customers}
          users={users}
        />
      )}

      {/* Pending Events Section */}
      <div className="mb-2">
        <div className="flex items-center">
          <FaClock className="text-orange-500 mr-2 hidden sm:block" size={20} />
          <FaClock className="text-orange-500 mr-2 sm:hidden" size={16} />
          <h2 className="text-lg sm:text-xl font-semibold">Pending Events</h2>
        </div>
      </div>
      {renderEventTable(pendingEvents, "Pending Events")}

      {/* Completed Events Section */}
      <div className="mb-2">
        <div className="flex items-center">
          <FaCheckCircle className="text-green-500 mr-2 hidden sm:block" size={20} />
          <FaCheckCircle className="text-green-500 mr-2 sm:hidden" size={16} />
          <h2 className="text-lg sm:text-xl font-semibold">Completed Events</h2>
        </div>
      </div>
      {renderEventTable(completedEvents, "Completed Events")}

      <Pagination
        currentPage={currentPage}
        totalPages={Math.ceil(totalItems / itemsPerPage)}
        onPageChange={handlePageChange}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={handleItemsPerPageChange}
      />
    </div>
  );
};

export default Events;
