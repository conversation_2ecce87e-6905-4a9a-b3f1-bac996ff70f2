"""
Quotation Template model module.
This module defines the QuotationTemplate model for managing DOCX templates for quotations.
"""
from app import db
from datetime import datetime

class QuotationTemplate(db.Model):
    """Model for quotation templates."""
    __tablename__ = "quotation_templates"

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    file_path = db.Column(db.String(2000), nullable=False)
    file_type = db.Column(db.String(10), default="docx", nullable=False)
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True, nullable=False)
    is_default = db.Column(db.Boolean, default=False, nullable=False)
    created_by = db.Column(db.Integer, db.<PERSON>ey("users.id"), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    user = db.relationship("User", backref=db.backref("quotation_templates", lazy="dynamic"))

    def to_dict(self):
        """Convert the quotation template to a dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "file_path": self.file_path,
            "file_type": self.file_type,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "created_by": self.created_by,
            "created_by_name": self.user.name if self.user else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

    def __repr__(self):
        return f"<QuotationTemplate {self.name}>"
