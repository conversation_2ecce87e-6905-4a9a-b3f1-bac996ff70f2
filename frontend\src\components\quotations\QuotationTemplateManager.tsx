import React, { useState, useEffect } from 'react';
import { QuotationTemplate } from '../../types/quotation_template';
import { 
  getAllQuotationTemplates, 
  createQuotationTemplate, 
  updateQuotationTemplate, 
  deleteQuotationTemplate, 
  setDefaultQuotationTemplate,
  downloadQuotationTemplate 
} from '../../services/quotationTemplateService';
import { useConfirmation } from '../../context/ConfirmationContext';
import { FaPlus, FaEdit, FaTrash, FaDownload, FaStar, FaUpload } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';
import { toast } from 'react-toastify';

const QuotationTemplateManager: React.FC = () => {
  const [templates, setTemplates] = useState<QuotationTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<QuotationTemplate | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const { showConfirmation } = useConfirmation();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true,
    is_default: false
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await getAllQuotationTemplates(false); // Get all templates including inactive
      setTemplates(response.templates);
    } catch (error: any) {
      console.error('Error fetching templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    setFormData({
      name: '',
      description: '',
      is_active: true,
      is_default: false
    });
    setSelectedFile(null);
    setShowModal(true);
  };

  const handleEdit = (template: QuotationTemplate) => {
    setEditingTemplate(template);
    setFormData({
      name: template.name,
      description: template.description || '',
      is_active: template.is_active,
      is_default: template.is_default
    });
    setSelectedFile(null);
    setShowModal(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editingTemplate && !selectedFile) {
      toast.error('Please select a file');
      return;
    }

    try {
      setSubmitting(true);

      if (editingTemplate) {
        // Update existing template
        await updateQuotationTemplate(editingTemplate.id, formData, selectedFile || undefined);
        toast.success('Template updated successfully');
      } else {
        // Create new template
        if (!selectedFile) {
          toast.error('File is required for new template');
          return;
        }
        await createQuotationTemplate(formData, selectedFile);
        toast.success('Template created successfully');
      }

      setShowModal(false);
      fetchTemplates();
    } catch (error: any) {
      console.error('Error saving template:', error);
      toast.error(error.response?.data?.error || 'Failed to save template');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = (template: QuotationTemplate) => {
    showConfirmation({
      title: 'Delete Template',
      message: `Are you sure you want to delete the template "${template.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      confirmButtonClass: 'bg-red-600 hover:bg-red-700',
      onConfirm: async () => {
        try {
          await deleteQuotationTemplate(template.id);
          toast.success('Template deleted successfully');
          fetchTemplates();
        } catch (error: any) {
          console.error('Error deleting template:', error);
          toast.error('Failed to delete template');
        }
      }
    });
  };

  const handleSetDefault = async (template: QuotationTemplate) => {
    try {
      await setDefaultQuotationTemplate(template.id);
      toast.success('Default template updated');
      fetchTemplates();
    } catch (error: any) {
      console.error('Error setting default template:', error);
      toast.error('Failed to set default template');
    }
  };

  const handleDownload = async (template: QuotationTemplate) => {
    try {
      const blob = await downloadQuotationTemplate(template.id);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${template.name}.docx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Error downloading template:', error);
      toast.error('Failed to download template');
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (!file.name.toLowerCase().endsWith('.docx')) {
        toast.error('Only DOCX files are allowed');
        return;
      }
      setSelectedFile(file);
    }
  };

  if (loading) {
    return <LoadingSpinner message="Loading templates..." />;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-amspm-text">Quotation Templates</h2>
        <button
          onClick={handleCreate}
          className="btn btn-primary flex items-center"
        >
          <FaPlus className="mr-2" /> Add Template
        </button>
      </div>

      <div className="bg-white dark:bg-dark-secondary rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Created By
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-dark-secondary divide-y divide-gray-200 dark:divide-gray-700">
            {templates.map((template) => (
              <tr key={template.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-gray-900 dark:text-dark-text">
                      {template.name}
                    </div>
                    {template.is_default && (
                      <FaStar className="ml-2 text-yellow-500" title="Default Template" />
                    )}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {template.description || '-'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    template.is_active 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {template.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {template.created_by_name || 'Unknown'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleDownload(template)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      title="Download"
                    >
                      <FaDownload />
                    </button>
                    <button
                      onClick={() => handleEdit(template)}
                      className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                      title="Edit"
                    >
                      <FaEdit />
                    </button>
                    {!template.is_default && (
                      <button
                        onClick={() => handleSetDefault(template)}
                        className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                        title="Set as Default"
                      >
                        <FaStar />
                      </button>
                    )}
                    <button
                      onClick={() => handleDelete(template)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      title="Delete"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {templates.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No templates found</p>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-dark-secondary rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 dark:text-dark-text mb-4">
              {editingTemplate ? 'Edit Template' : 'Add Template'}
            </h3>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Name *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="input w-full"
                  required
                  disabled={submitting}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="input w-full"
                  rows={3}
                  disabled={submitting}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Template File {!editingTemplate && '*'}
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="file"
                    accept=".docx"
                    onChange={handleFileChange}
                    className="hidden"
                    id="template-file"
                    disabled={submitting}
                  />
                  <label
                    htmlFor="template-file"
                    className="btn btn-secondary flex items-center cursor-pointer"
                  >
                    <FaUpload className="mr-2" />
                    {selectedFile ? selectedFile.name : 'Choose File'}
                  </label>
                </div>
                {!editingTemplate && (
                  <p className="text-xs text-gray-500 mt-1">Only DOCX files are allowed</p>
                )}
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    className="mr-2"
                    disabled={submitting}
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Active</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_default}
                    onChange={(e) => setFormData({ ...formData, is_default: e.target.checked })}
                    className="mr-2"
                    disabled={submitting}
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Set as Default</span>
                </label>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="btn btn-secondary"
                  disabled={submitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={submitting}
                >
                  {submitting ? 'Saving...' : (editingTemplate ? 'Update' : 'Create')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuotationTemplateManager;
