"""
Quotation Template controller module.
This module handles HTTP requests for quotation template operations.
"""
from flask import Blueprint, request, jsonify, send_file
from app.services.quotation_template_service import QuotationTemplateService
from app.utils.security import role_required, roles_required
from app.utils.rate_limit import rate_limit
from marshmallow import ValidationError
import logging
import io

logger = logging.getLogger(__name__)

quotation_template_bp = Blueprint("quotation_templates", __name__, url_prefix="/api/quotation-templates")
quotation_template_service = QuotationTemplateService()

@quotation_template_bp.route("", methods=["GET"])
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_all_quotation_templates():
    """
    Get all quotation templates.

    Returns:
        JSON: List of quotation templates.
    """
    try:
        active_only = request.args.get("active_only", "true").lower() == "true"
        templates = quotation_template_service.get_all_templates(active_only)
        return jsonify({"templates": templates}), 200
    except Exception as e:
        logger.error(f"Failed to fetch quotation templates: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_template_bp.route("/<int:template_id>", methods=["GET"])
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_quotation_template(template_id):
    """
    Get a quotation template by ID.

    Args:
        template_id: Template ID

    Returns:
        JSON: Quotation template data.
    """
    try:
        template = quotation_template_service.get_template_by_id(template_id)
        if not template:
            return jsonify({"error": f"Template with ID {template_id} not found"}), 404
        return jsonify(template), 200
    except Exception as e:
        logger.error(f"Failed to fetch quotation template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_template_bp.route("/default", methods=["GET"])
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_default_quotation_template():
    """
    Get the default quotation template.

    Returns:
        JSON: Default quotation template data.
    """
    try:
        template = quotation_template_service.get_default_template()
        if not template:
            return jsonify({"error": "No default template found"}), 404
        return jsonify(template), 200
    except Exception as e:
        logger.error(f"Failed to fetch default quotation template: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_template_bp.route("", methods=["POST"])
@role_required("administrator")
@rate_limit("10/minute")
def create_quotation_template():
    """
    Create a new quotation template.

    Returns:
        JSON: Created quotation template data.
    """
    try:
        # Check if file is provided
        if "file" not in request.files:
            return jsonify({"error": "No file part in the request"}), 400

        file = request.files["file"]

        # Check if file is empty
        if not file.filename or file.filename.strip() == "":
            return jsonify({"error": "No file selected"}), 400

        # Check file extension
        if not file.filename.lower().endswith(".docx"):
            return jsonify({"error": "Only DOCX files are allowed"}), 400

        # Get form data
        template_data = {
            "name": request.form.get("name"),
            "description": request.form.get("description"),
            "is_active": request.form.get("is_active", "true").lower() == "true",
            "is_default": request.form.get("is_default", "false").lower() == "true",
            "created_by": request.current_user.id
        }

        # Validate required fields
        if not template_data["name"]:
            return jsonify({"error": "Template name is required"}), 400

        # Create template
        template = quotation_template_service.create_template(template_data, file)

        logger.info(f"Created quotation template: {template['name']} (ID: {template['id']})")
        return jsonify(template), 201
    except ValidationError as e:
        logger.warning(f"Quotation template validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create quotation template: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_template_bp.route("/<int:template_id>", methods=["PUT"])
@role_required("administrator")
@rate_limit("10/minute")
def update_quotation_template(template_id):
    """
    Update a quotation template.

    Args:
        template_id: Template ID

    Returns:
        JSON: Updated quotation template data.
    """
    try:
        # Get file if provided
        file = request.files.get("file")

        # Check file extension if file is provided
        if file and not file.filename.lower().endswith(".docx"):
            return jsonify({"error": "Only DOCX files are allowed"}), 400

        # Get form data
        template_data = {}
        if request.form.get("name"):
            template_data["name"] = request.form.get("name")
        if request.form.get("description") is not None:
            template_data["description"] = request.form.get("description")
        if request.form.get("is_active") is not None:
            template_data["is_active"] = request.form.get("is_active").lower() == "true"
        if request.form.get("is_default") is not None:
            template_data["is_default"] = request.form.get("is_default").lower() == "true"

        # Update template
        template = quotation_template_service.update_template(template_id, template_data, file)
        if not template:
            return jsonify({"error": f"Template with ID {template_id} not found"}), 404

        logger.info(f"Updated quotation template: {template['name']} (ID: {template['id']})")
        return jsonify(template), 200
    except ValidationError as e:
        logger.warning(f"Quotation template validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update quotation template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_template_bp.route("/<int:template_id>", methods=["DELETE"])
@role_required("administrator")
@rate_limit("10/minute")
def delete_quotation_template(template_id):
    """
    Delete a quotation template.

    Args:
        template_id: Template ID

    Returns:
        JSON: Success message.
    """
    try:
        success = quotation_template_service.delete_template(template_id)
        if not success:
            return jsonify({"error": f"Template with ID {template_id} not found"}), 404

        logger.info(f"Deleted quotation template ID: {template_id}")
        return jsonify({"message": "Template deleted successfully"}), 200
    except Exception as e:
        logger.error(f"Failed to delete quotation template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_template_bp.route("/<int:template_id>/set-default", methods=["POST"])
@role_required("administrator")
@rate_limit("10/minute")
def set_default_quotation_template(template_id):
    """
    Set a template as the default.

    Args:
        template_id: Template ID

    Returns:
        JSON: Success message.
    """
    try:
        success = quotation_template_service.set_default_template(template_id)
        if not success:
            return jsonify({"error": f"Template with ID {template_id} not found"}), 404

        logger.info(f"Set quotation template {template_id} as default")
        return jsonify({"message": "Template set as default successfully"}), 200
    except Exception as e:
        logger.error(f"Failed to set default quotation template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_template_bp.route("/<int:template_id>/download", methods=["GET"])
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("30/minute")
def download_quotation_template(template_id):
    """
    Download a quotation template file.

    Args:
        template_id: Template ID

    Returns:
        File: Template file.
    """
    try:
        template = quotation_template_service.get_template_by_id(template_id)
        if not template:
            return jsonify({"error": f"Template with ID {template_id} not found"}), 404

        file_content = quotation_template_service.download_template_content(template_id)
        if not file_content:
            return jsonify({"error": "Failed to download template file"}), 500

        # Create a file-like object from bytes
        file_obj = io.BytesIO(file_content)
        filename = f"{template['name']}.docx"

        return send_file(
            file_obj,
            as_attachment=True,
            download_name=filename,
            mimetype="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
    except Exception as e:
        logger.error(f"Failed to download quotation template {template_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@quotation_template_bp.route("/generate", methods=["POST"])
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("10/minute")
def generate_quotation_document():
    """
    Generate a quotation document using a template.

    Returns:
        JSON: Generated document data.
    """
    try:
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request data is required"}), 400

        quotation_data = data.get("quotation_data")
        template_id = data.get("template_id")

        if not quotation_data:
            return jsonify({"error": "Quotation data is required"}), 400

        # Generate document
        document = quotation_template_service.generate_quotation_document(quotation_data, template_id)

        logger.info(f"Generated quotation document for quotation {quotation_data.get('id', 'unknown')}")
        return jsonify(document), 200
    except Exception as e:
        logger.error(f"Failed to generate quotation document: {str(e)}")
        return jsonify({"error": str(e)}), 500
