import api from "../api";
import { Quotation, QuotationItem, QuotationsResponse, QuotationStatus } from "../types/quotation";
import { AxiosError } from "axios";

/**
 * Error class for quotation-related errors
 */
export class QuotationError extends Error {
  status?: number;
  details?: any;

  constructor(message: string, status?: number, details?: any) {
    super(message);
    this.name = "QuotationError";
    this.status = status;
    this.details = details;
  }
}

/**
 * Handle API errors consistently
 */
const handleApiError = (error: any, defaultMessage: string): never => {
  if (error instanceof AxiosError && error.response) {
    // Log the error details for debugging
    console.error('API Error:', {
      status: error.response.status,
      data: error.response.data,
      url: error.config?.url,
      method: error.config?.method
    });

    // If we have validation details, include them in the error
    if (error.response.data?.details) {
      throw new QuotationError(
        error.response.data?.error || defaultMessage,
        error.response.status,
        error.response.data?.details
      );
    }

    throw new QuotationError(
      error.response.data?.error || defaultMessage,
      error.response.status
    );
  }

  // For non-Axios errors
  console.error('Non-Axios Error:', error);
  throw new QuotationError(error.message || defaultMessage);
};

/**
 * Get all quotations with pagination
 */
export const getAllQuotations = async (page: number = 1, perPage: number = 20): Promise<QuotationsResponse> => {
  try {
    const response = await api.get(`/quotations?page=${page}&per_page=${perPage}`);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, "Failed to fetch quotations");
  }
};

/**
 * Get a quotation by ID
 */
export const getQuotationById = async (quotationId: number): Promise<Quotation> => {
  try {
    const response = await api.get(`/quotations/${quotationId}`);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, `Failed to fetch quotation with ID ${quotationId}`);
  }
};

/**
 * Get quotations for a specific customer
 */
export const getQuotationsByCustomer = async (customerId: number, page: number = 1, perPage: number = 20): Promise<QuotationsResponse> => {
  try {
    const response = await api.get(`/quotations/customer/${customerId}?page=${page}&per_page=${perPage}`);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, `Failed to fetch quotations for customer ${customerId}`);
  }
};

/**
 * Search quotations by term
 */
export const searchQuotations = async (searchTerm: string, page: number = 1, perPage: number = 20): Promise<QuotationsResponse> => {
  try {
    const response = await api.get(`/quotations/search?q=${encodeURIComponent(searchTerm)}&page=${page}&per_page=${perPage}`);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, `Failed to search quotations with term "${searchTerm}"`);
  }
};

/**
 * Create a new quotation
 */
export const createQuotation = async (quotationData: Partial<Quotation>): Promise<Quotation> => {
  try {
    // Create a copy of the data without the items array
    const { items, ...dataToSend } = quotationData;
    const response = await api.post('/quotations', dataToSend);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, "Failed to create quotation");
  }
};

/**
 * Update an existing quotation
 */
export const updateQuotation = async (quotationId: number, quotationData: Partial<Quotation>): Promise<Quotation> => {
  try {
    // Filter out read-only fields and fields we don't want to update
    const {
      items,
      id,
      created_at,
      updated_at,
      customer_name,
      created_by_name,
      subtotal,
      discount_amount,
      total_excl_vat,
      vat_amount,
      total_incl_vat,
      ...dataToSend
    } = quotationData;

    console.log('Sending data to update quotation:', dataToSend);

    const response = await api.put(`/quotations/${quotationId}`, dataToSend);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, `Failed to update quotation with ID ${quotationId}`);
  }
};

/**
 * Delete a quotation
 */
export const deleteQuotation = async (quotationId: number): Promise<void> => {
  try {
    await api.delete(`/quotations/${quotationId}`);
  } catch (error: any) {
    handleApiError(error, `Failed to delete quotation with ID ${quotationId}`);
  }
};

/**
 * Add an item to a quotation
 */
export const addItemToQuotation = async (quotationId: number, itemData: Partial<QuotationItem>): Promise<QuotationItem> => {
  try {
    // Create a clean copy of the data to avoid duplicate fields
    const {
      id,
      quotation_id,
      product_name,
      product_code,
      total_price,
      created_at,
      updated_at,
      ...cleanData
    } = itemData;

    // Ensure description is present
    if (!cleanData.description && cleanData.product_id) {
      console.warn('Description is missing but product_id is present. This may cause validation errors.');
    }

    const response = await api.post(`/quotations/${quotationId}/items`, cleanData);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, `Failed to add item to quotation ${quotationId}`);
  }
};

/**
 * Update a quotation item
 */
export const updateQuotationItem = async (itemId: number, itemData: Partial<QuotationItem>): Promise<QuotationItem> => {
  try {
    // Create a clean copy of the data to avoid duplicate fields
    const {
      id,
      quotation_id,
      product_name,
      product_code,
      total_price,
      created_at,
      updated_at,
      ...cleanData
    } = itemData;

    // Ensure description is present
    if (!cleanData.description && cleanData.product_id) {
      console.warn('Description is missing but product_id is present. This may cause validation errors.');
    }

    const response = await api.put(`/quotations/items/${itemId}`, cleanData);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, `Failed to update quotation item ${itemId}`);
  }
};

/**
 * Delete a quotation item
 */
export const deleteQuotationItem = async (itemId: number): Promise<void> => {
  try {
    await api.delete(`/quotations/items/${itemId}`);
  } catch (error: any) {
    handleApiError(error, `Failed to delete quotation item ${itemId}`);
  }
};

/**
 * Generate a PDF for a quotation
 */
export const generateQuotationPdf = async (quotationId: number): Promise<{
  message: string;
  document: any;
}> => {
  try {
    const response = await api.post(`/quotations/${quotationId}/generate-pdf`);
    return response.data;
  } catch (error: any) {
    return handleApiError(error, `Failed to generate PDF for quotation ${quotationId}`);
  }
};



/**
 * Reject a quotation and immediately delete it along with the customer
 * if the customer has no other quotations
 */
export const rejectAndDeleteQuotation = async (quotationId: number): Promise<{
  success: boolean;
  message: string;
  customer_deleted: boolean;
}> => {
  try {
    // First update the status to rejected
    await updateQuotationStatus(quotationId, 'rejected');

    // Then process this specific rejected quotation
    const response = await api.post(`/quotations/${quotationId}/process-rejected`);
    return {
      success: true,
      message: 'Offerte succesvol geweigerd en verwijderd',
      customer_deleted: response.data.customer_deleted || false
    };
  } catch (error: any) {
    return handleApiError(error, `Failed to reject and delete quotation ${quotationId}`);
  }
};

/**
 * Update the status of a quotation (concept, sent, accepted, rejected)
 */
export const updateQuotationStatus = async (quotationId: number, status: QuotationStatus): Promise<Quotation> => {
  try {
    const response = await api.put(`/quotations/${quotationId}/status`, { status });
    return response.data;
  } catch (error: any) {
    return handleApiError(error, `Failed to update status for quotation ${quotationId}`);
  }
};

/**
 * Generate a document from a quotation using a template
 */
export const generateQuotationDocument = async (quotationData: any, templateId: number): Promise<{
  id: number;
  message: string;
}> => {
  try {
    const response = await api.post('/quotation-templates/generate', {
      quotation_data: quotationData,
      template_id: templateId
    });
    return response.data;
  } catch (error: any) {
    return handleApiError(error, 'Failed to generate quotation document');
  }
};

/**
 * Alias for getQuotationById for backward compatibility
 */
export const getQuotation = async (id: number): Promise<Quotation> => {
  return getQuotationById(id);
};
